#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Temu网页监听器
使用DrissionPage监听指定网页的网络请求和页面变化
"""

import time
import json
from datetime import datetime
from DrissionPage import Chromium
from DrissionPage.common import Settings


class TemuPageListener:
    """Temu页面监听器类"""
    
    def __init__(self, target_url="https://agentseller.temu.com/stock/fully-mgt/order-manage-urgency"):
        """
        初始化监听器
        
        Args:
            target_url (str): 要监听的目标网页URL
        """
        self.target_url = target_url
        self.browser = None
        self.tab = None
        self.is_listening = False
        self.captured_packets = []
        
        # 设置超时时间
        Settings.set_timeout(30)
        
    def start_browser(self):
        """启动浏览器并打开目标页面"""
        try:
            print(f"正在启动浏览器...")
            self.browser = Chromium()
            self.tab = self.browser.latest_tab
            
            print(f"正在访问目标网页: {self.target_url}")
            self.tab.get(self.target_url)
            
            # 等待页面加载完成
            self.tab.wait.load_complete()
            print("页面加载完成")
            
            return True
            
        except Exception as e:
            print(f"启动浏览器失败: {e}")
            return False
    
    def start_network_listener(self, filter_keywords=None):
        """
        启动网络监听器
        
        Args:
            filter_keywords (list): 要过滤的关键词列表，只监听包含这些关键词的请求
        """
        try:
            if filter_keywords is None:
                # 默认监听与Temu相关的请求
                filter_keywords = ['temu.com', 'api', 'order', 'stock', 'manage']
            
            print(f"开始监听网络请求，过滤关键词: {filter_keywords}")
            
            # 启动监听器，监听所有包含关键词的请求
            self.tab.listen.start(targets=filter_keywords, method=['GET', 'POST', 'PUT', 'DELETE'])
            self.is_listening = True
            
            print("网络监听器已启动")
            return True
            
        except Exception as e:
            print(f"启动网络监听器失败: {e}")
            return False
    
    def monitor_real_time(self, duration=300):
        """
        实时监听网络请求
        
        Args:
            duration (int): 监听持续时间（秒），默认5分钟
        """
        if not self.is_listening:
            print("请先启动网络监听器")
            return
        
        print(f"开始实时监听，持续时间: {duration}秒")
        start_time = time.time()
        packet_count = 0
        
        try:
            # 使用steps()方法实时获取数据包
            for packet in self.tab.listen.steps(timeout=5):
                if time.time() - start_time > duration:
                    break
                
                packet_count += 1
                self.captured_packets.append(packet)
                
                # 打印请求信息
                self.print_packet_info(packet, packet_count)
                
                # 保存重要的API响应数据
                if self.is_important_request(packet):
                    self.save_packet_data(packet)
        
        except KeyboardInterrupt:
            print("\n用户中断监听")
        except Exception as e:
            print(f"监听过程中出现错误: {e}")
        
        print(f"\n监听结束，共捕获 {packet_count} 个数据包")
    
    def wait_for_specific_request(self, target_pattern, timeout=60):
        """
        等待特定的网络请求
        
        Args:
            target_pattern (str): 要等待的请求URL模式
            timeout (int): 超时时间（秒）
        """
        print(f"等待包含 '{target_pattern}' 的请求...")
        
        try:
            # 设置新的监听目标
            self.tab.listen.set_targets(target_pattern)
            
            # 等待指定的请求
            packet = self.tab.listen.wait(timeout=timeout)
            
            if packet:
                print(f"捕获到目标请求: {packet.url}")
                self.print_packet_info(packet)
                return packet
            else:
                print("等待超时，未捕获到目标请求")
                return None
                
        except Exception as e:
            print(f"等待请求时出现错误: {e}")
            return None
    
    def print_packet_info(self, packet, index=None):
        """
        打印数据包信息
        
        Args:
            packet: 数据包对象
            index (int): 数据包序号
        """
        timestamp = datetime.now().strftime("%H:%M:%S")
        prefix = f"[{index}] " if index else ""
        
        print(f"\n{prefix}[{timestamp}] 捕获到请求:")
        print(f"  URL: {packet.url}")
        print(f"  方法: {packet.method}")
        print(f"  状态: {packet.response.status if packet.response else '未知'}")
        print(f"  资源类型: {packet.resourceType}")
        
        # 如果是POST请求，显示提交的数据
        if packet.method == 'POST' and packet.request.postData:
            print(f"  POST数据: {packet.request.postData[:200]}...")
    
    def is_important_request(self, packet):
        """
        判断是否为重要请求（API调用等）
        
        Args:
            packet: 数据包对象
            
        Returns:
            bool: 是否为重要请求
        """
        important_keywords = ['api', 'order', 'stock', 'manage', 'urgency', 'json']
        url_lower = packet.url.lower()
        
        return any(keyword in url_lower for keyword in important_keywords)
    
    def save_packet_data(self, packet):
        """
        保存重要的数据包数据到文件
        
        Args:
            packet: 数据包对象
        """
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"网络请求数据_{timestamp}.json"
            
            data = {
                'timestamp': timestamp,
                'url': packet.url,
                'method': packet.method,
                'status': packet.response.status if packet.response else None,
                'headers': dict(packet.request.headers) if packet.request.headers else {},
                'response_body': packet.response.body if packet.response else None
            }
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            print(f"  -> 数据已保存到: {filename}")
            
        except Exception as e:
            print(f"  -> 保存数据失败: {e}")
    
    def monitor_page_changes(self, check_interval=5):
        """
        监听页面内容变化
        
        Args:
            check_interval (int): 检查间隔（秒）
        """
        print(f"开始监听页面变化，检查间隔: {check_interval}秒")
        
        # 获取初始页面内容的关键信息
        previous_title = self.tab.title
        previous_url = self.tab.url
        
        try:
            while True:
                time.sleep(check_interval)
                
                current_title = self.tab.title
                current_url = self.tab.url
                
                # 检查标题变化
                if current_title != previous_title:
                    print(f"页面标题变化: {previous_title} -> {current_title}")
                    previous_title = current_title
                
                # 检查URL变化
                if current_url != previous_url:
                    print(f"页面URL变化: {previous_url} -> {current_url}")
                    previous_url = current_url
                
                # 可以添加更多页面元素的监听
                # 例如：检查特定元素的文本内容变化
                
        except KeyboardInterrupt:
            print("\n页面监听已停止")
    
    def stop_listening(self):
        """停止监听"""
        if self.is_listening:
            self.tab.listen.stop()
            self.is_listening = False
            print("监听已停止")
    
    def close_browser(self):
        """关闭浏览器"""
        if self.browser:
            self.browser.quit()
            print("浏览器已关闭")


def main():
    """主函数 - 演示监听器的使用"""
    # 创建监听器实例
    listener = TemuPageListener()
    
    try:
        # 启动浏览器
        if not listener.start_browser():
            return
        
        # 等待用户手动登录或进行必要的操作
        input("请在浏览器中完成登录等操作，然后按回车键继续...")
        
        # 启动网络监听
        if listener.start_network_listener():
            print("\n选择监听模式:")
            print("1. 实时监听网络请求")
            print("2. 等待特定请求")
            print("3. 监听页面变化")
            
            choice = input("请输入选择 (1-3): ").strip()
            
            if choice == '1':
                duration = int(input("请输入监听时长（秒，默认300）: ") or "300")
                listener.monitor_real_time(duration)
            
            elif choice == '2':
                pattern = input("请输入要等待的请求URL模式: ").strip()
                listener.wait_for_specific_request(pattern)
            
            elif choice == '3':
                listener.monitor_page_changes()
            
            else:
                print("无效选择，开始默认实时监听...")
                listener.monitor_real_time()
    
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    
    except Exception as e:
        print(f"程序运行出错: {e}")
    
    finally:
        # 清理资源
        listener.stop_listening()
        listener.close_browser()


if __name__ == "__main__":
    main()
