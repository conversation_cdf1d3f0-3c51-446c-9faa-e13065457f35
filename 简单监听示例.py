#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的Temu网页监听示例
快速上手DrissionPage网页监听功能
"""

from DrissionPage import Chromium
import time


def simple_network_monitor():
    """简单的网络请求监听示例"""
    
    # 创建浏览器实例
    browser = Chromium()
    tab = browser.latest_tab
    
    try:
        # 访问目标网页
        target_url = "https://agentseller.temu.com/stock/fully-mgt/order-manage-urgency"
        print(f"正在访问: {target_url}")
        tab.get(target_url)
        
        # 等待页面加载
        tab.wait.load_complete()
        print("页面加载完成")
        
        # 手动登录提示
        input("请在浏览器中完成登录，然后按回车键开始监听...")
        
        # 开始监听网络请求
        # 监听包含'api'、'order'、'stock'等关键词的请求
        print("开始监听网络请求...")
        tab.listen.start(['api', 'order', 'stock', 'manage'])
        
        # 实时获取网络请求
        packet_count = 0
        for packet in tab.listen.steps(count=10, timeout=5):  # 最多获取10个请求，每个请求等待5秒
            packet_count += 1
            
            print(f"\n[{packet_count}] 捕获到请求:")
            print(f"  URL: {packet.url}")
            print(f"  方法: {packet.method}")
            print(f"  状态码: {packet.response.status if packet.response else '未知'}")
            
            # 如果是API响应且包含JSON数据
            if packet.response and packet.response.body:
                try:
                    # 尝试解析JSON响应
                    if isinstance(packet.response.body, dict):
                        print(f"  响应数据: {str(packet.response.body)[:200]}...")
                except:
                    pass
        
        print(f"\n监听结束，共捕获 {packet_count} 个请求")
        
    except KeyboardInterrupt:
        print("\n用户中断监听")
    
    except Exception as e:
        print(f"监听过程中出现错误: {e}")
    
    finally:
        # 关闭浏览器
        browser.quit()
        print("浏览器已关闭")


def wait_for_specific_api():
    """等待特定API请求的示例"""
    
    browser = Chromium()
    tab = browser.latest_tab
    
    try:
        # 访问目标网页
        target_url = "https://agentseller.temu.com/stock/fully-mgt/order-manage-urgency"
        tab.get(target_url)
        tab.wait.load_complete()
        
        input("请完成登录，然后按回车键开始等待API请求...")
        
        # 开始监听特定的API请求
        print("等待包含'order'的API请求...")
        tab.listen.start('order')
        
        # 等待一个包含'order'的请求
        packet = tab.listen.wait(timeout=60)  # 等待60秒
        
        if packet:
            print(f"\n成功捕获到请求:")
            print(f"  URL: {packet.url}")
            print(f"  方法: {packet.method}")
            print(f"  状态码: {packet.response.status if packet.response else '未知'}")
            
            # 打印响应数据
            if packet.response and packet.response.body:
                print(f"  响应数据: {packet.response.body}")
        else:
            print("等待超时，未捕获到目标请求")
    
    except Exception as e:
        print(f"出现错误: {e}")
    
    finally:
        browser.quit()


def monitor_page_elements():
    """监听页面元素变化的示例"""
    
    browser = Chromium()
    tab = browser.latest_tab
    
    try:
        # 访问目标网页
        target_url = "https://agentseller.temu.com/stock/fully-mgt/order-manage-urgency"
        tab.get(target_url)
        tab.wait.load_complete()
        
        input("请完成登录，然后按回车键开始监听页面变化...")
        
        print("开始监听页面变化...")
        previous_title = tab.title
        
        # 监听页面变化
        for i in range(60):  # 监听60次，每次间隔5秒
            time.sleep(5)
            
            current_title = tab.title
            if current_title != previous_title:
                print(f"页面标题发生变化: {previous_title} -> {current_title}")
                previous_title = current_title
            
            # 检查特定元素是否存在或发生变化
            # 例如：检查订单数量、库存状态等
            try:
                # 这里可以根据实际页面结构添加具体的元素监听
                # order_count_element = tab.ele('订单数量的选择器')
                # if order_count_element:
                #     print(f"当前订单数量: {order_count_element.text}")
                pass
            except:
                pass
            
            print(f"第 {i+1} 次检查完成")
    
    except KeyboardInterrupt:
        print("\n监听被用户中断")
    
    except Exception as e:
        print(f"监听过程中出现错误: {e}")
    
    finally:
        browser.quit()


if __name__ == "__main__":
    print("Temu网页监听示例")
    print("1. 简单网络请求监听")
    print("2. 等待特定API请求")
    print("3. 监听页面元素变化")
    
    choice = input("请选择示例 (1-3): ").strip()
    
    if choice == '1':
        simple_network_monitor()
    elif choice == '2':
        wait_for_specific_api()
    elif choice == '3':
        monitor_page_elements()
    else:
        print("无效选择，运行默认示例...")
        simple_network_monitor()
