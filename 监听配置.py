#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
网页监听器配置文件
用于自定义监听参数和设置
"""

# 目标网页配置
TARGET_URL = "https://agentseller.temu.com/stock/fully-mgt/order-manage-urgency"

# 网络请求监听配置
NETWORK_LISTEN_CONFIG = {
    # 要监听的请求关键词
    'filter_keywords': [
        'api',
        'order',
        'stock', 
        'manage',
        'urgency',
        'temu.com',
        'agentseller'
    ],
    
    # 要监听的HTTP方法
    'methods': ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'],
    
    # 要监听的资源类型
    'resource_types': [
        'XHR',           # AJAX请求
        'Fetch',         # Fetch API请求
        'Document',      # 文档请求
        'Script',        # 脚本请求
        'Stylesheet'     # 样式表请求
    ],
    
    # 超时设置（秒）
    'timeout': 30,
    
    # 默认监听时长（秒）
    'default_duration': 300
}

# 重要请求识别配置
IMPORTANT_REQUEST_CONFIG = {
    # 重要请求的URL关键词
    'important_keywords': [
        'api/order',
        'api/stock',
        'api/manage',
        'urgency',
        'inventory',
        'product',
        'seller'
    ],
    
    # 重要的HTTP状态码
    'important_status_codes': [200, 201, 400, 401, 403, 404, 500],
    
    # 是否自动保存重要请求数据
    'auto_save': True,
    
    # 保存数据的目录
    'save_directory': './监听数据/'
}

# 页面监听配置
PAGE_MONITOR_CONFIG = {
    # 页面检查间隔（秒）
    'check_interval': 5,
    
    # 要监听的页面元素选择器
    'monitor_elements': [
        # 可以根据实际页面添加具体的选择器
        # 'css:.order-count',
        # 'css:.stock-status', 
        # 'css:.urgent-orders',
        # 'xpath://div[@class="notification"]'
    ],
    
    # 是否监听页面标题变化
    'monitor_title': True,
    
    # 是否监听URL变化
    'monitor_url': True,
    
    # 是否监听页面加载状态
    'monitor_loading': True
}

# 浏览器配置
BROWSER_CONFIG = {
    # 浏览器超时设置
    'timeout': 30,
    
    # 页面加载超时
    'page_load_timeout': 60,
    
    # 脚本执行超时
    'script_timeout': 30,
    
    # 是否启用无头模式
    'headless': False,
    
    # 浏览器窗口大小
    'window_size': (1920, 1080),
    
    # 用户代理字符串（可选）
    'user_agent': None,
    
    # 是否禁用图片加载（提高速度）
    'disable_images': False,
    
    # 是否禁用JavaScript
    'disable_javascript': False
}

# 日志配置
LOGGING_CONFIG = {
    # 是否启用详细日志
    'verbose': True,
    
    # 是否保存日志到文件
    'save_to_file': True,
    
    # 日志文件路径
    'log_file': './监听日志.txt',
    
    # 日志级别 (DEBUG, INFO, WARNING, ERROR)
    'log_level': 'INFO',
    
    # 是否显示时间戳
    'show_timestamp': True
}

# 数据导出配置
EXPORT_CONFIG = {
    # 支持的导出格式
    'formats': ['json', 'csv', 'txt'],
    
    # 默认导出格式
    'default_format': 'json',
    
    # 导出文件命名模式
    'filename_pattern': '网络请求数据_{timestamp}',
    
    # 是否包含响应体数据
    'include_response_body': True,
    
    # 是否包含请求头信息
    'include_headers': True,
    
    # 最大响应体大小（字节）
    'max_response_size': 1024 * 1024  # 1MB
}

# 通知配置
NOTIFICATION_CONFIG = {
    # 是否启用通知
    'enabled': False,
    
    # 通知方式 ('console', 'email', 'webhook')
    'methods': ['console'],
    
    # 触发通知的条件
    'triggers': {
        'error_status': [400, 401, 403, 404, 500],
        'important_keywords': ['error', 'fail', 'exception'],
        'response_time_threshold': 5.0  # 响应时间超过5秒
    },
    
    # 邮件通知配置（如果启用）
    'email': {
        'smtp_server': '',
        'smtp_port': 587,
        'username': '',
        'password': '',
        'to_addresses': []
    },
    
    # Webhook通知配置（如果启用）
    'webhook': {
        'url': '',
        'headers': {},
        'timeout': 10
    }
}

# 高级配置
ADVANCED_CONFIG = {
    # 是否启用请求去重
    'deduplicate_requests': True,
    
    # 去重的时间窗口（秒）
    'dedup_window': 60,
    
    # 最大缓存的请求数量
    'max_cached_requests': 1000,
    
    # 是否启用请求统计
    'enable_statistics': True,
    
    # 统计报告间隔（秒）
    'stats_interval': 300,
    
    # 是否启用性能监控
    'performance_monitoring': True
}


def get_config(config_name):
    """
    获取指定的配置
    
    Args:
        config_name (str): 配置名称
        
    Returns:
        dict: 配置字典
    """
    config_map = {
        'network': NETWORK_LISTEN_CONFIG,
        'important': IMPORTANT_REQUEST_CONFIG,
        'page': PAGE_MONITOR_CONFIG,
        'browser': BROWSER_CONFIG,
        'logging': LOGGING_CONFIG,
        'export': EXPORT_CONFIG,
        'notification': NOTIFICATION_CONFIG,
        'advanced': ADVANCED_CONFIG
    }
    
    return config_map.get(config_name, {})


def update_config(config_name, updates):
    """
    更新指定配置
    
    Args:
        config_name (str): 配置名称
        updates (dict): 要更新的配置项
    """
    config_map = {
        'network': NETWORK_LISTEN_CONFIG,
        'important': IMPORTANT_REQUEST_CONFIG,
        'page': PAGE_MONITOR_CONFIG,
        'browser': BROWSER_CONFIG,
        'logging': LOGGING_CONFIG,
        'export': EXPORT_CONFIG,
        'notification': NOTIFICATION_CONFIG,
        'advanced': ADVANCED_CONFIG
    }
    
    if config_name in config_map:
        config_map[config_name].update(updates)
        return True
    return False


def print_all_configs():
    """打印所有配置信息"""
    configs = {
        '网络监听配置': NETWORK_LISTEN_CONFIG,
        '重要请求配置': IMPORTANT_REQUEST_CONFIG,
        '页面监听配置': PAGE_MONITOR_CONFIG,
        '浏览器配置': BROWSER_CONFIG,
        '日志配置': LOGGING_CONFIG,
        '导出配置': EXPORT_CONFIG,
        '通知配置': NOTIFICATION_CONFIG,
        '高级配置': ADVANCED_CONFIG
    }
    
    for name, config in configs.items():
        print(f"\n=== {name} ===")
        for key, value in config.items():
            print(f"  {key}: {value}")


if __name__ == "__main__":
    print("网页监听器配置信息:")
    print(f"目标URL: {TARGET_URL}")
    print_all_configs()
